[project]
name = "ccxt-wrapper"
version = "0.1.0"
description = "A wrapper around ccxt"
authors = [{ name = "slugge", email = "<EMAIL>" }]
dependencies = [
    "ccxt==4.3.66",
    "flask[async]==3.0.3",
    "flask-mail==0.10.0",
    "pytest==8.2.2",
    "python-dotenv==1.0.1",
    "ruff==0.6.8",
    "psycopg[binary]==3.2.1",
    "pycares>=4.9.0",
    "urllib3==2.5.0",
]
requires-python = ">=3.12"

[tool.ruff.lint]
# 1. Enable flake8-bugbear (`B`) rules, in addition to the defaults.
select = ["E4", "E7", "E9", "F", "B"]

# 2. Avoid enforcing line-length violations (`E501`)
ignore = ["E501"]

# 3. Avoid trying to fix flake8-bugbear (`B`) violations.
unfixable = ["B"]

# 4. Ignore `E402` (import violations) in all `__init__.py` files, and in selected subdirectories.
[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["E402"]
"**/{tests,docs,tools}/*" = ["E402"]

[tool.ruff.format]
# 5. Use double quotes in `ruff format`.
quote-style = "double"

[tool.ruff]
# Allow lines to be as long as 127.
line-length = 127

[tool.ruff.lint.mccabe]
# Flag errors (`C901`) whenever the complexity level exceeds 5.
max-complexity = 10
