# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml --output-file requirements.txt
aiodns==3.5.0
    # via ccxt
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.15
    # via ccxt
aiosignal==1.4.0
    # via aiohttp
asgiref==3.9.1
    # via flask
attrs==25.3.0
    # via aiohttp
blinker==1.9.0
    # via
    #   flask
    #   flask-mail
ccxt==4.3.66
    # via ccxt-wrapper (pyproject.toml)
certifi==2025.7.14
    # via
    #   ccxt
    #   requests
cffi==1.17.1
    # via
    #   cryptography
    #   pycares
charset-normalizer==3.4.2
    # via requests
click==8.2.2
    # via flask
cryptography==45.0.5
    # via ccxt
flask==3.0.3
    # via
    #   ccxt-wrapper (pyproject.toml)
    #   flask-mail
flask-mail==0.10.0
    # via ccxt-wrapper (pyproject.toml)
frozenlist==1.7.0
    # via
    #   aiohttp
    #   aiosignal
idna==3.10
    # via
    #   requests
    #   yarl
iniconfig==2.1.0
    # via pytest
itsdangerous==2.2.0
    # via flask
jinja2==3.1.6
    # via flask
markupsafe==3.0.2
    # via
    #   jinja2
    #   werkzeug
multidict==6.6.3
    # via
    #   aiohttp
    #   yarl
packaging==25.0
    # via pytest
pluggy==1.6.0
    # via pytest
propcache==0.3.2
    # via
    #   aiohttp
    #   yarl
psycopg==3.2.1
    # via ccxt-wrapper (pyproject.toml)
psycopg-binary==3.2.1
    # via psycopg
pycares==4.9.0
    # via
    #   ccxt-wrapper (pyproject.toml)
    #   aiodns
pycparser==2.22
    # via cffi
pytest==8.2.2
    # via ccxt-wrapper (pyproject.toml)
python-dotenv==1.0.1
    # via ccxt-wrapper (pyproject.toml)
requests==2.32.4
    # via ccxt
ruff==0.6.8
    # via ccxt-wrapper (pyproject.toml)
setuptools==80.9.0
    # via ccxt
typing-extensions==4.14.1
    # via
    #   aiosignal
    #   ccxt
    #   psycopg
urllib3==2.5.0
    # via
    #   ccxt-wrapper (pyproject.toml)
    #   requests
werkzeug==3.1.3
    # via flask
yarl==1.20.1
    # via
    #   aiohttp
    #   ccxt
