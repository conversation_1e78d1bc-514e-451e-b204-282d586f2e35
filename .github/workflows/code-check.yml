name: Code Check(Ruff)

on: [push]

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.13", "3.12", "3.11"]
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install ruff
    - name: Analysing the code with pylint
      run: |
        # Run ruff with similar options to flake8
        ruff check . --exit-zero --line-length=127 --statistics
