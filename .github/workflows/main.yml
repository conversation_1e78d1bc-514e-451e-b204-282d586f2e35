name: Security Check(Bandit)

on:
  push:
    branches:
      - main
      - 'release/*'
  pull_request:
    branches:
      - main
      - 'release/*'

jobs:
  security:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v2

    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.x'  # Specify your Python version

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install bandit

    - name: Run bandit security check
      run: |
        bandit -r .  --skip B105,B101,B201,B104
